import pandas as pd
from datetime import datetime



def is_us_date(string):
    """Check if the string is a date in US format."""
    try:
        datetime.strptime(string, '%m/%d/%Y')
        return True
    except ValueError:
        return False

def convert_to_eu_format(string):
    """Convert a US date format to an EU date format."""
    return datetime.strptime(string, '%m/%d/%Y').strftime('%d/%m/%Y')

def convert_dates_in_csv(file_path):
    data = pd.read_csv(file_path)

    # Iterate over each cell and convert dates
    for col in data.columns:
        for row in range(len(data)):
            cell_content = str(data.at[row, col])
            if is_us_date(cell_content):
                data.at[row, col] = convert_to_eu_format(cell_content)

    # Save the modified data
    output_file_path = 'modified_' + file_path
    data.to_csv(output_file_path, index=False)
    print(f"File saved as {output_file_path}")



your_file = 'airbnb_tax_2025.csv'

# Example usage
convert_dates_in_csv(your_file)